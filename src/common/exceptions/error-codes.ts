export enum ErrorCode {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  PHONE_NOT_VERIFIED = 'PHONE_NOT_VERIFIED',

  USER_NOT_FOUND = 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  PASSWORD_REQUIRED = 'PASSWORD_REQUIRED',

  PARTNER_NOT_FOUND = 'PARTNER_NOT_FOUND',
  TAX_NUMBER_ALREADY_EXISTS = 'TAX_NUMBER_ALREADY_EXISTS',

  WORKER_NOT_FOUND = 'WORKER_NOT_FOUND',
  WORKER_NOT_EMPLOYED = 'WORKER_NOT_EMPLOYED',
  WORKER_NOT_APPROVED = 'WORKER_NOT_APPROVED',
  WORKER_NOT_ASSIGNED_TO_PROJECT = 'WORKER_NOT_ASSIGNED_TO_PROJECT',
  WORKER_ALREADY_PAUSED = 'WORKER_ALREADY_PAUSED',
  WORKER_ALREADY_ACTIVE = 'WORKER_ALREADY_ACTIVE',
  WORKER_ALREADY_FINISHED = 'WORKER_ALREADY_FINISHED',

  MANAGER_NOT_FOUND = 'MANAGER_NOT_FOUND',
  INSUFFICIENT_MANAGER_PERMISSIONS = 'INSUFFICIENT_MANAGER_PERMISSIONS',

  PROJECT_NOT_FOUND = 'PROJECT_NOT_FOUND',
  PROJECT_ALREADY_EXISTS = 'PROJECT_ALREADY_EXISTS',

  DAILY_REPORT_NOT_FOUND = 'DAILY_REPORT_NOT_FOUND',
  ACTIVE_REPORT_EXISTS = 'ACTIVE_REPORT_EXISTS',
  NO_ACTIVE_REPORT = 'NO_ACTIVE_REPORT',
  REPORT_ON_PAUSE = 'REPORT_ON_PAUSE',
  SUBMITTED_TIME_MISMATCH = 'SUBMITTED_TIME_MISMATCH',

  VALIDATION_REQUIREMENTS_MISSING = 'VALIDATION_REQUIREMENTS_MISSING',
  WORKER_IDS_EMPTY = 'WORKER_IDS_EMPTY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',

  REGISTRATION_CODE_NOT_FOUND = 'REGISTRATION_CODE_NOT_FOUND',
  REGISTRATION_CODE_EXPIRED = 'REGISTRATION_CODE_EXPIRED',
  REGISTRATION_CODE_INACTIVE = 'REGISTRATION_CODE_INACTIVE',
  REGISTRATION_REQUEST_NOT_FOUND = 'REGISTRATION_REQUEST_NOT_FOUND',
  INVALID_REGISTRATION_CODE = 'INVALID_REGISTRATION_CODE',

  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  FILE_UPLOAD_FAILED = 'FILE_UPLOAD_FAILED',

  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',

  VERIFICATION_TOKEN_NOT_FOUND = 'VERIFICATION_TOKEN_NOT_FOUND',
  VERIFICATION_TOKEN_EXPIRED = 'VERIFICATION_TOKEN_EXPIRED',
  INVALID_VERIFICATION_TYPE = 'INVALID_VERIFICATION_TYPE',

  RESET_TOKEN_NOT_FOUND = 'RESET_TOKEN_NOT_FOUND',
  RESET_TOKEN_EXPIRED = 'RESET_TOKEN_EXPIRED',

  NOTIFICATION_NOT_FOUND = 'NOTIFICATION_NOT_FOUND',
  NOTIFICATION_ALREADY_READ = 'NOTIFICATION_ALREADY_READ',

  EMPLOYMENT_HISTORY_NOT_FOUND = 'EMPLOYMENT_HISTORY_NOT_FOUND',
  EMPLOYMENT_ALREADY_ACTIVE = 'EMPLOYMENT_ALREADY_ACTIVE',

  ACTIVITY_HISTORY_NOT_FOUND = 'ACTIVITY_HISTORY_NOT_FOUND',

  FILE_PERMISSION_NOT_FOUND = 'FILE_PERMISSION_NOT_FOUND',
  FILE_PERMISSION_ALREADY_EXISTS = 'FILE_PERMISSION_ALREADY_EXISTS',
  INSUFFICIENT_FILE_PERMISSIONS = 'INSUFFICIENT_FILE_PERMISSIONS',

  PAUSE_HISTORY_NOT_FOUND = 'PAUSE_HISTORY_NOT_FOUND',
  PAUSE_ALREADY_ACTIVE = 'PAUSE_ALREADY_ACTIVE',
  NO_ACTIVE_PAUSE = 'NO_ACTIVE_PAUSE',

  PROJECT_MANAGER_ASSIGNMENT_NOT_FOUND = 'PROJECT_MANAGER_ASSIGNMENT_NOT_FOUND',
  PROJECT_MANAGER_ALREADY_ASSIGNED = 'PROJECT_MANAGER_ALREADY_ASSIGNED',
  CANNOT_REMOVE_LAST_MANAGER = 'CANNOT_REMOVE_LAST_MANAGER',

  FILE_SIZE_EXCEEDED = 'FILE_SIZE_EXCEEDED',
  UNSUPPORTED_FILE_FORMAT = 'UNSUPPORTED_FILE_FORMAT',
  FILE_CORRUPTED = 'FILE_CORRUPTED',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',

  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  DEVICE_ALREADY_REGISTERED = 'DEVICE_ALREADY_REGISTERED',
  INVALID_DEVICE_TOKEN = 'INVALID_DEVICE_TOKEN',
  DEVICE_NOT_OWNED = 'DEVICE_NOT_OWNED',

  CREDENTIAL_VERIFICATION_NOT_FOUND = 'CREDENTIAL_VERIFICATION_NOT_FOUND',
  CREDENTIAL_VERIFICATION_EXPIRED = 'CREDENTIAL_VERIFICATION_EXPIRED',
  CREDENTIAL_VERIFICATION_ALREADY_USED = 'CREDENTIAL_VERIFICATION_ALREADY_USED',
  INVALID_VERIFICATION_CODE = 'INVALID_VERIFICATION_CODE',

  PRESENCE_VALIDATION_NOT_FOUND = 'PRESENCE_VALIDATION_NOT_FOUND',
  PRESENCE_VALIDATION_EXPIRED = 'PRESENCE_VALIDATION_EXPIRED',
  PRESENCE_VALIDATION_ALREADY_COMPLETED = 'PRESENCE_VALIDATION_ALREADY_COMPLETED',
  INVALID_PRESENCE_DATA = 'INVALID_PRESENCE_DATA',

  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  RESOURCE_IN_USE = 'RESOURCE_IN_USE',
  DEPENDENCY_NOT_MET = 'DEPENDENCY_NOT_MET',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',

  FORBIDDEN_RESOURCE = 'FORBIDDEN_RESOURCE',
  FORBIDDEN_ROLE = 'FORBIDDEN_ROLE',
  NOT_AUTHORIZED = 'NOT_AUTHORIZED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST = 'BAD_REQUEST',
  CONFLICT = 'CONFLICT',
  NOT_FOUND = 'NOT_FOUND',

  // Missing error codes for existing exceptions
  REGISTRATION_CODE_REQUIRED = 'REGISTRATION_CODE_REQUIRED',
  PASSWORD_RESET_TOKEN_NOT_FOUND = 'PASSWORD_RESET_TOKEN_NOT_FOUND',
  PASSWORD_RESET_TOKEN_EXPIRED = 'PASSWORD_RESET_TOKEN_EXPIRED',
  SUPERVISOR_NOT_FOUND = 'SUPERVISOR_NOT_FOUND',
  SUPERVISOR_MISMATCH = 'SUPERVISOR_MISMATCH',
  PROJECT_NOT_MANAGED_BY_MANAGER = 'PROJECT_NOT_MANAGED_BY_MANAGER',
  FORBIDDEN_TO_FINISH_REPORT = 'FORBIDDEN_TO_FINISH_REPORT',
  FORBIDDEN_TO_CREATE_MANUAL_REPORT = 'FORBIDDEN_TO_CREATE_MANUAL_REPORT',
  WORKER_ALREADY_TERMINATED = 'WORKER_ALREADY_TERMINATED',
  WORKER_ON_NOTICE = 'WORKER_ON_NOTICE',
  WORKER_ALREADY_EMPLOYED = 'WORKER_ALREADY_EMPLOYED',
  INVALID_WORKER_ID = 'INVALID_WORKER_ID',

  // Additional error codes for direct NestJS exception usage
  REPORT_ON_PAUSE_VALIDATION = 'REPORT_ON_PAUSE_VALIDATION',
  VALIDATION_REQUIREMENTS_MISSING_DETAILED = 'VALIDATION_REQUIREMENTS_MISSING_DETAILED',
  WORKER_IDS_EMPTY_DETAILED = 'WORKER_IDS_EMPTY_DETAILED',
  GEO_COORDINATES_REQUIRED = 'GEO_COORDINATES_REQUIRED',
  PHOTO_REQUIRED = 'PHOTO_REQUIRED',
  WORKER_ID_SPECIFICATION_ERROR = 'WORKER_ID_SPECIFICATION_ERROR',
  WORKER_ID_REQUIRED = 'WORKER_ID_REQUIRED',
  MANAGER_PERMISSION_RESTRICTION = 'MANAGER_PERMISSION_RESTRICTION',
  INVALID_USER_CONTEXT = 'INVALID_USER_CONTEXT',
  INVALID_USER_ENTITY = 'INVALID_USER_ENTITY',
  RATE_LIMIT_PRESENCE_VALIDATION = 'RATE_LIMIT_PRESENCE_VALIDATION',
  VALIDATION_REQUEST_EXPIRED = 'VALIDATION_REQUEST_EXPIRED',
  PRESENCE_VALIDATION_TOO_LATE = 'PRESENCE_VALIDATION_TOO_LATE',
  CODE_NAME_ALREADY_EXISTS = 'CODE_NAME_ALREADY_EXISTS',
}

export const ErrorMessages: Record<ErrorCode, string> = {
  [ErrorCode.INVALID_CREDENTIALS]: 'Invalid email or password',
  [ErrorCode.TOKEN_EXPIRED]: 'Token has expired',
  [ErrorCode.INVALID_TOKEN]: 'Invalid token',
  [ErrorCode.UNAUTHORIZED]: 'Unauthorized access',
  [ErrorCode.FORBIDDEN]: 'Access forbidden',
  [ErrorCode.EMAIL_NOT_VERIFIED]: 'Email address is not verified',
  [ErrorCode.PHONE_NOT_VERIFIED]: 'Phone number is not verified',

  [ErrorCode.USER_NOT_FOUND]: 'User not found',
  [ErrorCode.EMAIL_ALREADY_EXISTS]: 'Email address already exists',
  [ErrorCode.INVALID_PASSWORD]: 'Invalid password',
  [ErrorCode.PASSWORD_REQUIRED]: 'Password is required',

  [ErrorCode.PARTNER_NOT_FOUND]: 'Partner not found',
  [ErrorCode.TAX_NUMBER_ALREADY_EXISTS]: 'Tax number already exists',

  [ErrorCode.WORKER_NOT_FOUND]: 'Worker not found',
  [ErrorCode.WORKER_NOT_EMPLOYED]: 'Worker is not employed',
  [ErrorCode.WORKER_NOT_APPROVED]: 'Worker is not approved',
  [ErrorCode.WORKER_NOT_ASSIGNED_TO_PROJECT]:
    'Worker is not assigned to a project',
  [ErrorCode.WORKER_ALREADY_PAUSED]: 'Worker is already paused',
  [ErrorCode.WORKER_ALREADY_ACTIVE]: 'Worker is already active',
  [ErrorCode.WORKER_ALREADY_FINISHED]:
    'Worker has already finished their report',

  [ErrorCode.MANAGER_NOT_FOUND]: 'Manager not found',
  [ErrorCode.INSUFFICIENT_MANAGER_PERMISSIONS]:
    'Insufficient manager permissions',

  [ErrorCode.PROJECT_NOT_FOUND]: 'Project not found',
  [ErrorCode.PROJECT_ALREADY_EXISTS]: 'Project already exists',

  [ErrorCode.DAILY_REPORT_NOT_FOUND]: 'Daily report not found',
  [ErrorCode.ACTIVE_REPORT_EXISTS]:
    'Cannot start a new report while another report is active',
  [ErrorCode.NO_ACTIVE_REPORT]: 'No active report found',
  [ErrorCode.REPORT_ON_PAUSE]: 'Worker has paused report',
  [ErrorCode.SUBMITTED_TIME_MISMATCH]:
    'Submitted time differs significantly from calculated working time',

  [ErrorCode.PRESENCE_VALIDATION_NOT_FOUND]: 'Presence validation not found',
  [ErrorCode.VALIDATION_REQUIREMENTS_MISSING]:
    'At least one validation is required (photo or geo)',
  [ErrorCode.WORKER_IDS_EMPTY]: 'Worker IDs cannot be empty',
  [ErrorCode.RATE_LIMIT_EXCEEDED]:
    'Rate limit exceeded for presence validation requests',

  [ErrorCode.REGISTRATION_CODE_NOT_FOUND]: 'Registration code not found',
  [ErrorCode.REGISTRATION_CODE_EXPIRED]: 'Registration code has expired',
  [ErrorCode.REGISTRATION_CODE_INACTIVE]: 'Registration code is inactive',
  [ErrorCode.REGISTRATION_REQUEST_NOT_FOUND]: 'Registration request not found',
  [ErrorCode.INVALID_REGISTRATION_CODE]: 'Invalid registration code',

  [ErrorCode.FILE_NOT_FOUND]: 'File not found',
  [ErrorCode.INVALID_FILE_TYPE]: 'Invalid file type',
  [ErrorCode.FILE_TOO_LARGE]: 'File size exceeds maximum limit',
  [ErrorCode.FILE_UPLOAD_FAILED]: 'File upload failed',

  [ErrorCode.SESSION_NOT_FOUND]: 'Session not found',

  [ErrorCode.VERIFICATION_TOKEN_NOT_FOUND]: 'Verification token not found',
  [ErrorCode.VERIFICATION_TOKEN_EXPIRED]: 'Verification token has expired',
  [ErrorCode.INVALID_VERIFICATION_TYPE]: 'Invalid verification type',

  [ErrorCode.RESET_TOKEN_NOT_FOUND]: 'Reset token not found',
  [ErrorCode.RESET_TOKEN_EXPIRED]: 'Reset token has expired',

  [ErrorCode.NOTIFICATION_NOT_FOUND]: 'Notification not found',
  [ErrorCode.NOTIFICATION_ALREADY_READ]:
    'Notification is already marked as read',

  [ErrorCode.EMPLOYMENT_HISTORY_NOT_FOUND]:
    'Employment history record not found',
  [ErrorCode.EMPLOYMENT_ALREADY_ACTIVE]: 'Employment is already active',

  [ErrorCode.ACTIVITY_HISTORY_NOT_FOUND]: 'Activity history record not found',

  [ErrorCode.FILE_PERMISSION_NOT_FOUND]: 'File permission not found',
  [ErrorCode.FILE_PERMISSION_ALREADY_EXISTS]: 'File permission already exists',
  [ErrorCode.INSUFFICIENT_FILE_PERMISSIONS]: 'Insufficient file permissions',

  [ErrorCode.PAUSE_HISTORY_NOT_FOUND]: 'Pause history record not found',
  [ErrorCode.PAUSE_ALREADY_ACTIVE]: 'Pause is already active',
  [ErrorCode.NO_ACTIVE_PAUSE]: 'No active pause found',

  [ErrorCode.PROJECT_MANAGER_ASSIGNMENT_NOT_FOUND]:
    'Project manager assignment not found',
  [ErrorCode.PROJECT_MANAGER_ALREADY_ASSIGNED]:
    'Manager is already assigned to this project',
  [ErrorCode.CANNOT_REMOVE_LAST_MANAGER]:
    'Cannot remove the last manager from project',

  [ErrorCode.FILE_SIZE_EXCEEDED]: 'File size exceeds maximum allowed limit',
  [ErrorCode.UNSUPPORTED_FILE_FORMAT]: 'Unsupported file format',
  [ErrorCode.FILE_CORRUPTED]: 'File is corrupted or invalid',
  [ErrorCode.STORAGE_QUOTA_EXCEEDED]: 'Storage quota exceeded',

  [ErrorCode.DEVICE_NOT_FOUND]: 'Device not found',
  [ErrorCode.DEVICE_ALREADY_REGISTERED]: 'Device is already registered',
  [ErrorCode.INVALID_DEVICE_TOKEN]: 'Invalid device token',
  [ErrorCode.DEVICE_NOT_OWNED]: 'Device is not owned by the current user',

  [ErrorCode.CREDENTIAL_VERIFICATION_NOT_FOUND]:
    'Credential verification not found',
  [ErrorCode.CREDENTIAL_VERIFICATION_EXPIRED]:
    'Credential verification has expired',
  [ErrorCode.CREDENTIAL_VERIFICATION_ALREADY_USED]:
    'Credential verification has already been used',
  [ErrorCode.INVALID_VERIFICATION_CODE]: 'Invalid verification code',

  [ErrorCode.PRESENCE_VALIDATION_EXPIRED]: 'Presence validation has expired',
  [ErrorCode.PRESENCE_VALIDATION_ALREADY_COMPLETED]:
    'Presence validation has already been completed',
  [ErrorCode.INVALID_PRESENCE_DATA]: 'Invalid presence validation data',

  [ErrorCode.OPERATION_NOT_ALLOWED]: 'Operation not allowed in current state',
  [ErrorCode.RESOURCE_IN_USE]: 'Resource is currently in use',
  [ErrorCode.DEPENDENCY_NOT_MET]: 'Required dependency not met',
  [ErrorCode.QUOTA_EXCEEDED]: 'Quota exceeded',

  [ErrorCode.VALIDATION_ERROR]: 'Validation error',
  [ErrorCode.INTERNAL_SERVER_ERROR]: 'Internal server error',
  [ErrorCode.BAD_REQUEST]: 'Bad request',
  [ErrorCode.CONFLICT]: 'Conflict',
  [ErrorCode.NOT_FOUND]: 'Resource not found',

  [ErrorCode.FORBIDDEN_RESOURCE]: 'Forbidden resource',
  [ErrorCode.FORBIDDEN_ROLE]: 'Forbidden role',
  [ErrorCode.NOT_AUTHORIZED]: 'Not authorized',

  // Missing error messages for existing exceptions
  [ErrorCode.REGISTRATION_CODE_REQUIRED]: 'Registration code is required',
  [ErrorCode.PASSWORD_RESET_TOKEN_NOT_FOUND]: 'Password reset request not found',
  [ErrorCode.PASSWORD_RESET_TOKEN_EXPIRED]: 'Password reset request expired',
  [ErrorCode.SUPERVISOR_NOT_FOUND]: 'Supervisor not found',
  [ErrorCode.SUPERVISOR_MISMATCH]: 'Forbidden to change report status, supervisor mismatch',
  [ErrorCode.PROJECT_NOT_MANAGED_BY_MANAGER]: 'Forbidden to change report status, project not managed',
  [ErrorCode.FORBIDDEN_TO_FINISH_REPORT]: 'Not authorized to finish this report',
  [ErrorCode.FORBIDDEN_TO_CREATE_MANUAL_REPORT]: 'Forbidden to create manual report (project not managed)',
  [ErrorCode.WORKER_ALREADY_TERMINATED]: 'Worker is already terminated',
  [ErrorCode.WORKER_ON_NOTICE]: 'Worker is currently on notice period to be terminated',
  [ErrorCode.WORKER_ALREADY_EMPLOYED]: 'Worker is already employed, you have to quit first',
  [ErrorCode.INVALID_WORKER_ID]: 'Invalid worker id',

  // Additional error messages for direct NestJS exception usage
  [ErrorCode.REPORT_ON_PAUSE_VALIDATION]: 'Worker has paused report',
  [ErrorCode.VALIDATION_REQUIREMENTS_MISSING_DETAILED]: 'At least one validation is required (photo or geo)',
  [ErrorCode.WORKER_IDS_EMPTY_DETAILED]: 'Worker IDs cannot be empty',
  [ErrorCode.GEO_COORDINATES_REQUIRED]: 'Geo coordinates are required',
  [ErrorCode.PHOTO_REQUIRED]: 'Photo is required',
  [ErrorCode.WORKER_ID_SPECIFICATION_ERROR]: 'Worker ID must not be specified for worker reports',
  [ErrorCode.WORKER_ID_REQUIRED]: 'Worker ID must be specified',
  [ErrorCode.MANAGER_PERMISSION_RESTRICTION]: 'You cannot create a code for a manager with all permissions, only partner has this permission',
  [ErrorCode.INVALID_USER_CONTEXT]: 'Invalid user',
  [ErrorCode.INVALID_USER_ENTITY]: 'User does not have a valid entityId. Please log in again.',
  [ErrorCode.RATE_LIMIT_PRESENCE_VALIDATION]: 'You can only create one presence validation per 5 minutes',
  [ErrorCode.VALIDATION_REQUEST_EXPIRED]: 'Validation request has expired',
  [ErrorCode.PRESENCE_VALIDATION_TOO_LATE]: 'Too late, you cannot send a late presence validation',
  [ErrorCode.CODE_NAME_ALREADY_EXISTS]: 'Code with this name already exists',
};