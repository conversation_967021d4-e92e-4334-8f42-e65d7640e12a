import { ZodError, ZodIssue } from 'zod';
import { 
  ValidationErrorCode, 
  ValidationErrorMessages, 
  FieldValidationErrorCodes 
} from './validation-error-codes';

/**
 * Interface for coded validation errors
 */
export interface CodedValidationError {
  field: string;
  message: string;
  code: ValidationErrorCode;
  value?: any;
}

/**
 * Interface for validation error response
 */
export interface ValidationErrorResponse {
  validationErrors: Record<string, CodedValidationError[]>;
  errorCount: number;
}

/**
 * Maps Zod validation errors to coded validation errors
 */
export class ValidationErrorMapper {
  /**
   * Maps a ZodError to coded validation errors
   * @param zodError - The Zod validation error
   * @returns Mapped validation errors with codes
   */
  static mapZodError(zodError: ZodError): ValidationErrorResponse {
    const validationErrors: Record<string, CodedValidationError[]> = {};
    let errorCount = 0;

    for (const issue of zodError.issues) {
      const field = this.getFieldPath(issue);
      const codedError = this.mapZodIssue(issue, field);
      
      if (!validationErrors[field]) {
        validationErrors[field] = [];
      }
      
      validationErrors[field].push(codedError);
      errorCount++;
    }

    return {
      validationErrors,
      errorCount,
    };
  }

  /**
   * Maps a single Zod issue to a coded validation error
   * @param issue - The Zod issue
   * @param field - The field name
   * @returns Coded validation error
   */
  private static mapZodIssue(issue: ZodIssue, field: string): CodedValidationError {
    const code = this.getValidationErrorCode(issue, field);
    const message = this.getValidationErrorMessage(issue, field, code);

    return {
      field,
      message,
      code,
      value: issue.received,
    };
  }

  /**
   * Gets the field path from a Zod issue
   * @param issue - The Zod issue
   * @returns Field path as string
   */
  private static getFieldPath(issue: ZodIssue): string {
    if (issue.path.length === 0) {
      return 'root';
    }
    return issue.path.join('.');
  }

  /**
   * Gets the appropriate validation error code for a Zod issue
   * @param issue - The Zod issue
   * @param field - The field name
   * @returns Validation error code
   */
  private static getValidationErrorCode(issue: ZodIssue, field: string): ValidationErrorCode {
    // Check for field-specific error code mappings first
    const fieldMappings = FieldValidationErrorCodes[field];
    if (fieldMappings) {
      const specificCode = fieldMappings[issue.code];
      if (specificCode) {
        return specificCode;
      }
    }

    // Fall back to generic mappings based on Zod issue code
    switch (issue.code) {
      case 'invalid_string':
        if (issue.validation === 'email') {
          return ValidationErrorCode.EMAIL_INVALID_FORMAT;
        }
        return ValidationErrorCode.STRING_INVALID_FORMAT;

      case 'too_small':
        if (issue.type === 'string') {
          if (field === 'password') {
            return ValidationErrorCode.PASSWORD_TOO_SHORT;
          }
          if (field === 'phoneNumber') {
            return ValidationErrorCode.PHONE_TOO_SHORT;
          }
          return ValidationErrorCode.STRING_TOO_SHORT;
        }
        if (issue.type === 'number') {
          return ValidationErrorCode.NUMBER_TOO_SMALL;
        }
        if (issue.type === 'array') {
          return ValidationErrorCode.ARRAY_TOO_SHORT;
        }
        return ValidationErrorCode.FIELD_INVALID;

      case 'too_big':
        if (issue.type === 'string') {
          if (field === 'password') {
            return ValidationErrorCode.PASSWORD_TOO_LONG;
          }
          if (field === 'phoneNumber') {
            return ValidationErrorCode.PHONE_TOO_LONG;
          }
          return ValidationErrorCode.STRING_TOO_LONG;
        }
        if (issue.type === 'number') {
          return ValidationErrorCode.NUMBER_TOO_BIG;
        }
        if (issue.type === 'array') {
          return ValidationErrorCode.ARRAY_TOO_LONG;
        }
        return ValidationErrorCode.FIELD_INVALID;

      case 'invalid_type':
        if (issue.expected === 'string') {
          return ValidationErrorCode.STRING_REQUIRED;
        }
        if (issue.expected === 'number') {
          return ValidationErrorCode.NUMBER_REQUIRED;
        }
        if (issue.expected === 'boolean') {
          return ValidationErrorCode.BOOLEAN_REQUIRED;
        }
        if (issue.expected === 'array') {
          return ValidationErrorCode.ARRAY_REQUIRED;
        }
        if (issue.expected === 'object') {
          return ValidationErrorCode.OBJECT_REQUIRED;
        }
        if (issue.expected === 'date') {
          return ValidationErrorCode.DATE_REQUIRED;
        }
        return ValidationErrorCode.FIELD_REQUIRED;

      case 'invalid_enum_value':
        if (field === 'citizenship') {
          return ValidationErrorCode.CITIZENSHIP_INVALID;
        }
        return ValidationErrorCode.ENUM_INVALID_VALUE;

      case 'invalid_union':
        return ValidationErrorCode.UNION_INVALID_VALUE;

      case 'invalid_date':
        return ValidationErrorCode.DATE_INVALID_FORMAT;

      case 'custom':
        // Handle custom validation errors (like phone number country code)
        if (field === 'phoneNumber' && issue.message?.includes('country code')) {
          return ValidationErrorCode.PHONE_INVALID_COUNTRY_CODE;
        }
        return ValidationErrorCode.CUSTOM_VALIDATION_FAILED;

      case 'invalid_literal':
        return ValidationErrorCode.FIELD_INVALID;

      case 'unrecognized_keys':
        return ValidationErrorCode.OBJECT_INVALID_FORMAT;

      case 'invalid_arguments':
      case 'invalid_return_type':
        return ValidationErrorCode.VALIDATION_FAILED;

      default:
        return ValidationErrorCode.VALIDATION_FAILED;
    }
  }

  /**
   * Gets the appropriate error message for a validation error
   * @param issue - The Zod issue
   * @param field - The field name
   * @param code - The validation error code
   * @returns Error message
   */
  private static getValidationErrorMessage(
    issue: ZodIssue, 
    field: string, 
    code: ValidationErrorCode
  ): string {
    // Use custom message if available and meaningful
    if (issue.message && issue.message !== 'Required' && issue.message !== 'Invalid') {
      return issue.message;
    }

    // Use the standardized message for the error code
    return ValidationErrorMessages[code];
  }

  /**
   * Converts validation errors to the legacy format for backward compatibility
   * @param codedErrors - Coded validation errors
   * @returns Legacy validation errors format
   */
  static toLegacyFormat(codedErrors: ValidationErrorResponse): Record<string, string[]> {
    const legacyErrors: Record<string, string[]> = {};
    
    for (const [field, errors] of Object.entries(codedErrors.validationErrors)) {
      legacyErrors[field] = errors.map(error => error.message);
    }
    
    return legacyErrors;
  }
}
