import { Module, forwardRef } from '@nestjs/common';

import { WorkerController } from './worker.controller';
import { WorkersController } from './workers.controller';
import { WorkersService } from './workers.service';
import { ActivityHistoryModule } from '../activity-history/activity-history.module';
import { DailyReportsModule } from '../daily-reports/daily-reports.module';
import { ManagersModule } from '../managers/managers.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { RegistrationCodesModule } from '../registration-codes/registration-codes.module';
import { RegistrationRequestsModule } from '../registration-requests/registration-requests.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { ProjectsModule } from '../projects/projects.module';
import { PresenceValidationsModule } from '../presence-validations/presence-validations.module';

@Module({
  imports: [
    forwardRef(() => DailyReportsModule),
    forwardRef(() => RegistrationCodesModule),
    forwardRef(() => RegistrationRequestsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => ActivityHistoryModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => NotificationsModule),
    forwardRef(() => ProjectsModule),
    forwardRef(() => PresenceValidationsModule),
  ],
  controllers: [WorkersController, WorkerController],
  providers: [WorkersService],
  exports: [WorkersService],
})
export class WorkersModule {}
