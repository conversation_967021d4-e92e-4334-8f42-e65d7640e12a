import { Module, forwardRef } from '@nestjs/common';

import { PresenceValidationsController } from './presence-validations.controller';
import { PresenceValidationsService } from './presence-validations.service';
import { DailyReportsModule } from '../daily-reports/daily-reports.module';
import { FilesModule } from '../files/files.module';
import { ManagersModule } from '../managers/managers.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { UpdatesModule } from '../updates/updates.module';
import { UsersModule } from '../users/users.module';
import { WorkersModule } from '../workers/workers.module';

@Module({
  imports: [
    forwardRef(() => DailyReportsModule),
    FilesModule,
    forwardRef(() => NotificationsModule),
    forwardRef(() => UpdatesModule),
    forwardRef(() => WorkersModule),
    forwardRef(() => ManagersModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [PresenceValidationsController],
  providers: [PresenceValidationsService],
  exports: [PresenceValidationsService],
})
export class PresenceValidationsModule {}
