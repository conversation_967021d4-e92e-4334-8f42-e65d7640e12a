import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, varchar } from 'drizzle-orm/pg-core';

import { generateId } from '@/common/utils/generateId';

import { managers } from './manager.entity';
import { partnerSpecialties } from './partner-specialty.entity';
import { projects } from './project.entity';
import { registrationCodes } from './registration-code.entity';
import { users } from './user.entity';
import { workLocations } from './work-location.entity';
import { workers } from './worker.entity';

export const partners = pgTable('partner', {
  id: varchar('id', { length: 36 })
    .primaryKey()
    .$defaultFn(() => generateId()),
  userId: varchar('user_id', { length: 36 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  taxNumber: varchar('tax_number', { length: 20 }).unique(),
  registrationAddress: text('registration_address'),
  workLocation: text('work_location'),
  companyName: text('company_name'),
  managerApprovalAllowed: boolean('manager_approval_allowed').default(false),
});

export const partnersRelations = relations(partners, ({ one, many }) => ({
  user: one(users, { fields: [partners.userId], references: [users.id] }),
  managers: many(managers),
  codes: many(registrationCodes),
  specialties: many(partnerSpecialties),
  workLocations: many(workLocations),
  projects: many(projects),
  workers: many(workers),
}));
