import { Module, forwardRef } from '@nestjs/common';

import { CredentialVerificationController } from './credential-verification.controller';
import { CredentialVerificationService } from './credential-verification.service';
import { UsersModule } from '../users/users.module';
import { NotificationsModule } from '../notifications/notifications.module';

@Module({ 
  imports: [forwardRef(() => UsersModule), forwardRef(() =>NotificationsModule)],
  controllers: [CredentialVerificationController],
  providers: [CredentialVerificationService],
  exports: [CredentialVerificationService],
})
export class CredentialVerificationModule {}
