import { RedisService } from '@liaoliaots/nestjs-redis';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import Redis from 'ioredis';

import {
  ApprovalState,
  EmploymentStatus,
  Role,
  TERMINATED_STATUSES,
} from '@/common/enums';
import {
  EmailAlreadyExistsException,
  InvalidPasswordException,
  PasswordRequiredException,
  UserNotFoundException,
} from '@/common/exceptions/custom-exceptions';
import { ManagerPermissionType } from '@/common/permissions/manager-permissions.config';
import { Forwarded } from '@/common/types';
import { verifyPassword } from '@/common/utils/hash';

import { CredentialVerificationService } from '../credential-verification/credential-verification.service';
import { Database } from '../db/db.module';
import { CreateUserDto } from './dto/create-user.dto';
import {
  UpdatePersonalInfoDto,
  UpdateUserDto,
  UpdateUserEmailDto,
} from './dto/update-user.dto';
import { UserWithRoleInfoDto } from './dto/user.dto';
import {
  ManagerRoleInfo,
  PartnerRoleInfo,
  RoleInfo,
  WorkerRoleInfo,
  WorkerStatus,
} from './types/role.types';
import { users } from '../db/entities/user.entity';
import {
  ManagerDto,
  ManagerWithProjectsDto,
} from '../managers/dto/manager.dto';
import { PartnerDto } from '../partners/dto/partner.dto';
import { SecurityNotificationsService } from '../security-notifications/security-notifications.service';
import { UpdatesGateway } from '../updates/updates.gateway';
import { UpdatesEvents } from '../updates/updates.types';
import { WorkerDto } from '../workers/dto/worker.dto';
import { RequestUserType } from '../auth/dto/request-user.dto';

@Injectable()
export class UsersService {
  private readonly redis: Redis;
  private readonly NULL_SENTINEL = 'null';

  constructor(
    @Inject('DB') private readonly db: Database,
    @Inject(forwardRef(() => CredentialVerificationService))
    private readonly credentialVerificationService: Forwarded<CredentialVerificationService>,
    private readonly redisService: RedisService,
    @Inject(forwardRef(() => UpdatesGateway))
    private readonly updatesGateway: Forwarded<UpdatesGateway>,
    @Inject(forwardRef(() => SecurityNotificationsService))
    private readonly securityNotificationsService: Forwarded<SecurityNotificationsService>,
  ) {
    this.redis = this.redisService.getOrThrow();
  }

  async create(createUserDto: CreateUserDto) {
    return (
      await this.db
        .insert(users)
        .values(createUserDto)
        .returning({ id: users.id })
        .onConflictDoNothing()
    )[0];
  }

  findAll() {
    return this.db.query.users.findMany();
  }

  findOne(id: string, options?: FindOptions, tx?: Database) {
    const query = tx ? tx.query.users : this.db.query.users;

    return query.findFirst({
      where: (users, { eq }) => eq(users.id, id),
      columns: !options?.includeSensitiveData
        ? {
            id: false,
            hashedPassword: false,
            documentScan: false,
            isEmailVerified: false,
            isPhoneVerified: false,
            role: false,
          }
        : undefined,
    });
  }

  async getUserMetaData(id: string) {
    const user = await this.findOneWithRelations(id);
    if (!user) throw new UserNotFoundException();
    const roleInfo = await this.generateUserRoleInfo(user);
    await this.setEntityId(id, roleInfo.entityId);

    return {
      roleInfo,
      verifications: {
        isEmailVerified: user.isEmailVerified,
        isPhoneVerified: user.isPhoneVerified,
      },
    };
  }

  findOneWithRelations(id: string, options?: FindOptions) {
    return this.db.query.users.findFirst({
      where: (users, { eq }) => eq(users.id, id),
      columns: !options?.includeSensitiveData
        ? {
            hashedPassword: false,
          }
        : undefined,
      with: {
        managers: {
          with: {
            partner: true,
            projectManagers: true,
          },
        },
        partners: true,
        workers: true,
      },
    });
  }

  findByEmail(email: string, options?: FindOptions) {
    return options?.includeSensitiveData
      ? this.db.query.users.findFirst({
          where: (users, { eq }) => eq(users.email, email),
          columns: !options?.includeSensitiveData
            ? {
                hashedPassword: false,
              }
            : undefined,
        })
      : this.db.query.users.findFirst({
          where: (users, { eq }) => eq(users.email, email),
          columns: !options?.includeSensitiveData
            ? {
                hashedPassword: false,
              }
            : undefined,
        });
  }

  findByPhoneNumber(phoneNumber: string, options?: FindOptions) {
    return options?.includeSensitiveData
      ? this.db.query.users.findFirst({
          where: (users, { eq }) => eq(users.phoneNumber, phoneNumber),
          columns: !options?.includeSensitiveData
            ? {
                hashedPassword: false,
              }
            : undefined,
        })
      : this.db.query.users.findFirst({
          where: (users, { eq }) => eq(users.phoneNumber, phoneNumber),
          columns: !options?.includeSensitiveData
            ? {
                hashedPassword: false,
              }
            : undefined,
        });
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    return await this.db.transaction(async (tx) => {
      const user = await tx.query.users.findFirst({
        where: (users, { eq }) => eq(users.id, id),
      });

      if (!user) {
        throw new UserNotFoundException();
      }

      return (
        await tx
          .update(users)
          .set(updateUserDto)
          .where(eq(users.id, id))
          .returning()
      )[0];
    });
  }

  async updatePersonalInfo(id: string, updateUserDto: UpdatePersonalInfoDto) {
    const user = await this.update(id, updateUserDto);

    if (user.email) {
      await this.securityNotificationsService.sendSecurityNotification(
        user.email,
        'profile_updated',
      );
    }

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.USER_UPDATED,
      payload: {
        userId: id,
        action: 'personal_info_changed',
        changes: {
          personalInfo: true,
        },
      },
    });
  }

  async updateEmail(id: string, { email, password }: UpdateUserEmailDto) {
    const user = await this.findOne(id, { includeSensitiveData: true });
    if (!user) throw new UserNotFoundException();
    if (!password.length) throw new PasswordRequiredException();

    const isValidPassword = await verifyPassword(user.hashedPassword, password);

    if (!isValidPassword) {
      throw new InvalidPasswordException();
    }

    const userByEmail = await this.findByEmail(email);
    if (userByEmail && userByEmail.id !== id) {
      throw new EmailAlreadyExistsException();
    }

    if (user.email !== email) {
      const oldEmail = user.email;

      await this.db.update(users).set({ email }).where(eq(users.id, id));
      await this.updateVerificationStatus(id, 'email', false);

      await this.credentialVerificationService.createAndSendEmail({
        userId: id,
        type: 'email',
        contact: email,
      });

      await this.securityNotificationsService.sendSecurityNotification(
        oldEmail,
        'email_changed',
        { oldEmail, newEmail: email },
      );

      await this.securityNotificationsService.sendSecurityNotification(
        email,
        'email_changed',
        { oldEmail, newEmail: email },
      );

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.USER_UPDATED,
        payload: {
          userId: id,
          action: 'email_changed',
          changes: {
            email: true,
            isEmailVerified: false,
          },
        },
      });
    }
  }

  async updatePhoneNumber(id: string, phoneNumber: string) {
    const user = await this.findOne(id);
    if (!user) throw new UserNotFoundException();

    if (user.phoneNumber !== phoneNumber) {
      await this.db.update(users).set({ phoneNumber }).where(eq(users.id, id));

      await this.credentialVerificationService.createAndSendSms({
        userId: id,
        type: 'phone',
        contact: phoneNumber,
      });

      if (user.email) {
        await this.securityNotificationsService.sendSecurityNotification(
          user.email,
          'phone_changed',
        );
      }

      await this.updatesGateway.sendMessage({
        type: UpdatesEvents.USER_UPDATED,
        payload: {
          userId: id,
          action: 'phone_changed',
          changes: {
            phoneNumber: true,
            isPhoneVerified: false,
          },
        },
      });
    }
  }

  async updateVerificationStatus(
    id: string,
    type: 'email' | 'phone',
    verified: boolean,
  ) {
    const updateData =
      type === 'email'
        ? { isEmailVerified: verified }
        : { isPhoneVerified: verified };

    await this.db.update(users).set(updateData).where(eq(users.id, id));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.USER_UPDATED,
      payload: {
        userId: id,
        action: 'verification_changed',
        changes: {
          [type === 'email' ? 'isEmailVerified' : 'isPhoneVerified']: verified,
        },
      },
    });
  }

  async getUserRoleInfo(id: string): Promise<RoleInfo> {
    const user = await this.findOneWithRelations(id);
    if (!user) throw new UserNotFoundException();

    const roleInfo = this.generateUserRoleInfo(user);
    await this.setEntityId(id, roleInfo.entityId);
    return roleInfo;
  }

  private generateUserRoleInfo(
    user: UserWithRoleInfoDto & { id: string; role: string },
  ): RoleInfo {
    switch (user.role) {
      case Role.Manager:
        return this.generateManagerInfo(user.managers[0]);
      case Role.Worker:
        return this.generateWorkerInfo(user.workers);
      case Role.Partner:
        return this.generatePartnerInfo(user.partners[0]);
      default:
        throw new Error(`Invalid role: ${user.role}`);
    }
  }

  private generateManagerInfo(
    manager: ManagerWithProjectsDto,
  ): ManagerRoleInfo {
    if (!manager) {
      throw new Error('Manager data not found');
    }

    return {
      type: Role.Manager,
      entityId: manager.id,
      approvalState: manager.approvalState as ApprovalState | null,
      permissionType: manager.permissionType as ManagerPermissionType | null,
      hasProjects: manager.projectManagers.length > 0,
      employmentStatus: manager.employmentStatus as EmploymentStatus | null,
    };
  }

  private generateWorkerInfo(workers: WorkerDto[]): WorkerRoleInfo {
    const sortedWorkers = this.sortWorkersByDate(workers);
    const { activeWorker, pendingWorker, effectiveWorker } =
      this.categorizeWorkers(sortedWorkers);

    const isIndependent = !activeWorker && !pendingWorker;

    return {
      type: Role.Worker,
      entityId: isIndependent ? null : effectiveWorker?.id || null,
      independent: isIndependent,
      approvalState: (effectiveWorker?.approvalState as ApprovalState) || null,
      employmentStatus: isIndependent
        ? null
        : (effectiveWorker?.employmentStatus as EmploymentStatus) || null,
    };
  }

  private generatePartnerInfo(partner: PartnerDto): PartnerRoleInfo {
    if (!partner) {
      throw new Error('Partner data not found');
    }

    return {
      type: Role.Partner,
      entityId: partner.id,
    };
  }

  private sortWorkersByDate(workers: WorkerDto[]): WorkerDto[] {
    return [...workers].sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
    );
  }

  private categorizeWorkers(sortedWorkers: WorkerDto[]): WorkerStatus {
    const activeWorker = sortedWorkers.find(
      (w) =>
        !TERMINATED_STATUSES.includes(
          w.employmentStatus as
            | EmploymentStatus.Quit
            | EmploymentStatus.Terminated,
        ) && w.approvalState !== ApprovalState.Rejected,
    );

    const pendingWorker = activeWorker
      ? null
      : sortedWorkers.find(
          (w) =>
            w.approvalState === ApprovalState.Pending &&
            w.employmentStatus === EmploymentStatus.Inactive,
        );

    const latestWorker = sortedWorkers[0];

    const effectiveWorker = activeWorker || pendingWorker || latestWorker;

    return {
      activeWorker,
      pendingWorker,
      latestWorker,
      effectiveWorker,
    };
  }

  async getEntityId(userId: string): Promise<string | null> {
    const entityId = await this.redis.get(`user:${userId}:role:current`);

    if (entityId === this.NULL_SENTINEL) {
      return null;
    }

    if (entityId) {
      return entityId;
    }

    const entityIdFromDB = await this.getEntityIdFromDB(userId);
    await this.setEntityId(userId, entityIdFromDB);
    return entityIdFromDB;
  }

  async setEntityId(userId: string, entityId: string | null): Promise<void> {
    const EXPIRES_IN_SECONDS = 60 * 60 * 24 * 30; // 30 days
    const key = `user:${userId}:role:current`;
    const value = entityId || this.NULL_SENTINEL;

    await this.redis.set(key, value, 'EX', EXPIRES_IN_SECONDS);
  }

  async getEntityIdFromDB(userId: string): Promise<string | null> {
    const roleInfo = await this.getUserRoleInfo(userId);
    return roleInfo.entityId;
  }

  private async anonymizeUser(userId: string, tx?: Database) {
    const user = await this.findOne(
      userId,
      {
        includeSensitiveData: true,
      },
      tx,
    );

    if (!user) throw new UserNotFoundException();

    const database = tx || this.db;
    await database
      .update(users)
      .set({
        email: `${user.id}@anonymized.com`,
        phoneNumber: '**********',
        avatarId: null,
        isEmailVerified: false,
        isPhoneVerified: false,
      })
      .where(eq(users.id, userId));

    await this.updatesGateway.sendMessage({
      type: UpdatesEvents.USER_DELETED,
      payload: { userId },
    });
  }

  async deleteUser(user: RequestUserType) {
    await this.db.transaction(async (tx) => {
      await this.anonymizeUser(user.id, tx);
      await tx
        .update(users)
        .set({ deletedAt: new Date() })
        .where(eq(users.id, user.id));
    });
  }
}

type FindOptions = {
  includeSensitiveData?: boolean;
};
